import discum
import threading

# ⚠️ Your Discord user token goes here (DO NOT share this with anyone!)
USER_TOKEN = "NzEyOTI1OTQxMTM4OTgwOTM0.GiaRVi.zQNBJK5VfTcj3FO-ErQ4-lCJT0qsj_aVsoQoU0"

def send_discord_message(user_name: str, message: str):
    bot = discum.Client(token=USER_TOKEN, log=False)

    def start_bot():
        @bot.gateway.command
        def on_ready(resp):
            if resp.event.ready_supplemental:
                print("[<PERSON>] Logged in.")
                user_found = False
                for guild_id in bot.gateway.session.guild_ids:
                    members = bot.getGuildMembers(guild_id, channelID=None, presences=False)
                    bot.gateway.fetchMembers(guild_id, keep="all", wait=1)
                    bot.gateway.run(auto_reconnect=False)  # Pause and allow member fetch

                    for m in bot.gateway.session.guild(guild_id).members:
                        username = m["username"]
                        # Get nickname (display name) if available
                        nickname = None
                        if "nick" in m and m["nick"]:
                            nickname = m["nick"]
                        
                        # Check if user_name is contained in username or nickname
                        if (user_name.lower() in username.lower() or 
                            (nickname and user_name.lower() in nickname.lower())):
                            user_id = m["userId"]
                            display_name = nickname or username
                            print(f"[Jarvis] Found user: {display_name} ({user_id})")
                            bot.sendMessage(user_id, message)
                            user_found = True
                            break
                    if user_found:
                        break

                if not user_found:
                    print("[Jarvis] User not found.")
                bot.gateway.close()

        bot.gateway.run()

    thread = threading.Thread(target=start_bot)
    thread.start()

def call_discord_user(user_name: str):
    bot = discum.Client(token=USER_TOKEN, log=False)
    
    def start_call():
        @bot.gateway.command
        def on_ready(resp):
            if resp.event.ready_supplemental:
                print("[Jarvis] Logged in.")
                user_found = False
                for guild_id in bot.gateway.session.guild_ids:
                    members = bot.getGuildMembers(guild_id, channelID=None, presences=False)
                    bot.gateway.fetchMembers(guild_id, keep="all", wait=1)
                    bot.gateway.run(auto_reconnect=False)
                    
                    for m in bot.gateway.session.guild(guild_id).members:
                        username = m["username"]
                        if username.lower() == user_name.lower():
                            user_id = m["userId"]
                            print(f"[Jarvis] Found user: {username} ({user_id})")
                            
                            # Create DM channel and initiate call
                            channel_id = bot.createDM([user_id]).json()["id"]
                            bot.call(channel_id)
                            print(f"[Jarvis] Calling {username}...")
                            
                            user_found = True
                            break
                    if user_found:
                        break
                
                if not user_found:
                    print("[Jarvis] User not found.")
                bot.gateway.close()
        
        bot.gateway.run()
    
    thread = threading.Thread(target=start_call)
    thread.start()
