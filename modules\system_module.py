import os
import ctypes
from pycaw.pycaw import AudioUtilities, IAudioEndpointVolume
from ctypes import cast, POINTER
from comtypes import CLSCTX_ALL
import pyautogui

# Volume control setup
def _get_volume_interface():
    devices = AudioUtilities.GetSpeakers()
    interface = devices.Activate(IAudioEndpointVolume._iid_, CLSCTX_ALL, None)
    return cast(interface, POINTER(IAudioEndpointVolume))

def set_volume(percent):
    """Set system volume: 0 to 100"""
    percent = max(0, min(percent, 100))  # Clamp range
    volume = _get_volume_interface()
    volume.SetMasterVolumeLevelScalar(percent / 100.0, None)
    print(f"[Jarvis] Volume set to {percent}%")

def volume_up(step=10):
    volume = _get_volume_interface()
    current = volume.GetMasterVolumeLevelScalar()
    new = min(current + (step / 100.0), 1.0)
    volume.SetMasterVolumeLevelScalar(new, None)
    print(f"[<PERSON>] Volume increased to {int(new * 100)}%")

def volume_down(step=10):
    volume = _get_volume_interface()
    current = volume.GetMasterVolumeLevelScalar()
    new = max(current - (step / 100.0), 0.0)
    volume.SetMasterVolumeLevelScalar(new, None)
    print(f"[Jarvis] Volume decreased to {int(new * 100)}%")

def mute_volume():
    volume = _get_volume_interface()
    volume.SetMute(1, None)
    print("[Jarvis] Volume muted")

def unmute_volume():
    volume = _get_volume_interface()
    volume.SetMute(0, None)
    print("[Jarvis] Volume unmuted")

# Other system functions
def lock_screen():
    ctypes.windll.user32.LockWorkStation()
    print("[Jarvis] Locking screen")

def shutdown():
    os.system("shutdown /s /t 0")
    print("[Jarvis] Shutting down...")

def restart():
    os.system("shutdown /r /t 0")
    print("[Jarvis] Restarting...")

def open_app(path):
    os.startfile(path)
    print(f"[Jarvis] Opening: {path}")

def press_key(key):
    pyautogui.press(key)
    print(f"[Jarvis] Pressed {key}")

def type_message(message):
    pyautogui.typewrite(message)
    print(f"[Jarvis] Typed out message: {message}")

def unlock_screen(passcode=None):
    """Unlock the screen by clicking and optionally entering a passcode"""
    try:
        # Click on the screen to wake it up
        pyautogui.click()

        # Wait a moment for the screen to respond
        pyautogui.sleep(0.5)

        # Press space or enter to dismiss lock screen/activate password field
        pyautogui.press('space')

        # If passcode is provided, enter it
        if passcode:
            pyautogui.sleep(0.5)  # Wait for password field to appear
            pyautogui.typewrite(passcode)
            pyautogui.press('enter')
            print("[Jarvis] Screen unlock attempted with passcode")
        else:
            print("[Jarvis] Screen unlock attempted")

    except Exception as e:
        print(f"[Jarvis] Error unlocking screen: {e}")
