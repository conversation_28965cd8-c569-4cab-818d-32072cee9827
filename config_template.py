# Configuration Template for <PERSON> Voice Assistant
# Copy this file to config.py and fill in your actual values

# Spotify Configuration
SPOTIFY_CLIENT_ID = "your_spotify_client_id_here"
SPOTIFY_CLIENT_SECRET = "your_spotify_client_secret_here"
SPOTIFY_REDIRECT_URI = "http://localhost:8888/callback"

# Discord Configuration (⚠️ Use at your own risk - against Discord ToS)
DISCORD_USER_TOKEN = "your_discord_user_token_here"

# Voice Recognition Settings
VOSK_MODEL_PATH = "vosk-model-small-en-us-0.15"
AUDIO_SAMPLE_RATE = 16000
AUDIO_BUFFER_SIZE = 8192

# Wake Words
WAKE_WORDS = ["hey jarvis", "jarvis"]

# Text-to-Speech Settings
TTS_RATE = 200  # Speech rate (words per minute)
TTS_VOLUME = 0.9  # Volume level (0.0 to 1.0)

# System Settings
VOLUME_STEP = 10  # Volume adjustment step (percentage)
