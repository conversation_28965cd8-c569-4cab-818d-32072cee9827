# Jarvis Voice Assistant

A voice-controlled assistant that can control Spotify, Discord, and system functions.

## Features

- Voice recognition using Vosk
- Text-to-speech responses
- Spotify music control
- Discord messaging
- System volume control
- Screen lock, shutdown, restart

## Setup

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Download Vosk Model

Download the Vosk model and extract it to the project directory:
- Download: https://alphacephei.com/vosk/models/vosk-model-small-en-us-0.15.zip
- Extract to: `vosk-model-small-en-us-0.15/`

### 3. Configure API Keys

#### Spotify Setup:
1. Go to https://developer.spotify.com/dashboard
2. Create a new app
3. Set redirect URI to: `http://localhost:8888/callback`
4. Copy Client ID and Client Secret
5. Set environment variables:
   ```bash
   set SPOTIFY_CLIENT_ID=your_client_id_here
   set SPOTIFY_CLIENT_SECRET=your_client_secret_here
   ```

#### Discord Setup:
1. Get your Discord user token (⚠️ Use at your own risk)
2. Edit `modules/discord_module.py` and replace `YOUR_USER_TOKEN_HERE`

### 4. Run the Assistant

```bash
python main.py
```

## Voice Commands

- "Hey Jarvis, play [song name]" - Play music on Spotify
- "Hey Jarvis, send discord message to [username] saying [message]" - Send Discord DM
- "Hey Jarvis, volume up/down" - Control system volume
- "Hey Jarvis, mute/unmute" - Mute/unmute system
- "Hey Jarvis, lock screen" - Lock the screen
- "Hey Jarvis, shutdown/restart" - System power control
- "Hey Jarvis, report for duty" - Status check

## Security Notes

- Keep your Discord token private
- Use environment variables for sensitive data
- Discord user tokens are against ToS - use at your own risk

## Troubleshooting

- Ensure microphone permissions are granted
- Make sure Spotify is running and logged in
- Check that audio devices are properly configured
- Verify all API credentials are correct
